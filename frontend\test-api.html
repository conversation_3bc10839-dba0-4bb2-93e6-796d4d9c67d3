<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testRecipes()">Test Recipes API</button>
    <button onclick="testRecipeIngredients()">Test Recipe Ingredients API</button>
    <button onclick="testFullFlow()">Test Full Flow</button>
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:3000';

        async function testRecipes() {
            try {
                const response = await fetch(`${API_BASE_URL}/recipes`);
                const data = await response.json();
                document.getElementById('results').innerHTML = `
                    <h2>Recipes API Response:</h2>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h2>Error:</h2>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testRecipeIngredients() {
            try {
                const response = await fetch(`${API_BASE_URL}/recipes/1/ingredients`);
                const data = await response.json();
                document.getElementById('results').innerHTML = `
                    <h2>Recipe Ingredients API Response:</h2>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    <h3>Ingredients Array:</h3>
                    <pre>${JSON.stringify(data.data?.ingredients, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h2>Error:</h2>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testFullFlow() {
            try {
                // Test the full flow like the frontend does
                const recipesResponse = await fetch(`${API_BASE_URL}/recipes`);
                const recipesData = await recipesResponse.json();

                let html = '<h2>Full Flow Test:</h2>';
                html += `<h3>Recipes:</h3><pre>${JSON.stringify(recipesData.data, null, 2)}</pre>`;

                if (recipesData.data && recipesData.data.length > 0) {
                    const recipe = recipesData.data[0];
                    const ingredientsResponse = await fetch(`${API_BASE_URL}/recipes/${recipe.id}/ingredients`);
                    const ingredientsData = await ingredientsResponse.json();

                    html += `<h3>Ingredients for Recipe ${recipe.id}:</h3>`;
                    html += `<pre>${JSON.stringify(ingredientsData, null, 2)}</pre>`;

                    if (ingredientsData.data && ingredientsData.data.ingredients) {
                        html += '<h3>Formatted Ingredients:</h3><ul>';
                        ingredientsData.data.ingredients.forEach(ing => {
                            html += `<li>${ing.name}: ${ing.quantity} ${ing.quantity_unit} (${ing.fodmap_level})</li>`;
                        });
                        html += '</ul>';
                    }
                }

                document.getElementById('results').innerHTML = html;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h2>Error:</h2>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
