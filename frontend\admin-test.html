<!DOCTYPE html>
<html>
<head>
    <title>Admin Test</title>
</head>
<body>
    <h1>Admin Authentication Test</h1>
    <button onclick="testAuth()">Test Admin Auth</button>
    <div id="result"></div>

    <script>
        async function testAuth() {
            try {
                const response = await fetch('http://localhost:3000/admin/auth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        password: 'Dupadupa123'
                    })
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<pre>Error: ' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
