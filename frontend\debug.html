<!DOCTYPE html>
<html>
<head>
    <title>Debug Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Frontend Debug Tool</h1>
    
    <div class="section">
        <h2>API Tests</h2>
        <button onclick="testRecipesAPI()">Test Recipes API</button>
        <button onclick="testIngredientsAPI()">Test Recipe Ingredients API</button>
        <button onclick="testFullFlow()">Test Full Frontend Flow</button>
        <div id="apiResults"></div>
    </div>

    <div class="section">
        <h2>Console Output</h2>
        <div id="consoleOutput"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000';
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(type, ...args) {
            const output = document.getElementById('consoleOutput');
            const div = document.createElement('div');
            div.style.color = type === 'error' ? 'red' : 'black';
            div.innerHTML = `<strong>[${type.toUpperCase()}]</strong> ${args.join(' ')}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };

        async function testRecipesAPI() {
            try {
                console.log('Testing recipes API...');
                const response = await fetch(`${API_BASE_URL}/recipes`);
                const data = await response.json();
                console.log('Recipes API response:', data);
                
                document.getElementById('apiResults').innerHTML = `
                    <h3>Recipes API Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error testing recipes API:', error);
                document.getElementById('apiResults').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testIngredientsAPI() {
            try {
                console.log('Testing recipe ingredients API...');
                const response = await fetch(`${API_BASE_URL}/recipes/1/ingredients`);
                const data = await response.json();
                console.log('Recipe ingredients API response:', data);
                
                document.getElementById('apiResults').innerHTML = `
                    <h3>Recipe Ingredients API Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error testing recipe ingredients API:', error);
                document.getElementById('apiResults').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testFullFlow() {
            try {
                console.log('Testing full frontend flow...');
                
                // Step 1: Load recipes
                console.log('Step 1: Loading recipes...');
                const recipesResponse = await fetch(`${API_BASE_URL}/recipes`);
                const recipesData = await recipesResponse.json();
                console.log('Recipes loaded:', recipesData);
                
                if (!recipesData.data || recipesData.data.length === 0) {
                    console.error('No recipes found');
                    return;
                }
                
                // Step 2: Load ingredients for first recipe
                const recipe = recipesData.data[0];
                console.log(`Step 2: Loading ingredients for recipe ${recipe.id}...`);
                const ingredientsResponse = await fetch(`${API_BASE_URL}/recipes/${recipe.id}/ingredients`);
                const ingredientsData = await ingredientsResponse.json();
                console.log('Ingredients loaded:', ingredientsData);
                
                // Step 3: Process ingredients
                if (ingredientsData.data && ingredientsData.data.ingredients) {
                    console.log('Step 3: Processing ingredients...');
                    const ingredients = ingredientsData.data.ingredients;
                    console.log('Ingredients array:', ingredients);
                    
                    let html = '<h3>Processed Ingredients:</h3><ul>';
                    ingredients.forEach(ing => {
                        console.log('Processing ingredient:', ing);
                        html += `<li><strong>${ing.name}</strong>: ${ing.quantity} ${ing.quantity_unit} (FODMAP: ${ing.fodmap_level})</li>`;
                    });
                    html += '</ul>';
                    
                    document.getElementById('apiResults').innerHTML = html;
                    console.log('Full flow completed successfully');
                } else {
                    console.error('No ingredients found in response');
                }
                
            } catch (error) {
                console.error('Error in full flow test:', error);
                document.getElementById('apiResults').innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        // Auto-run basic test on load
        window.addEventListener('load', () => {
            console.log('Debug page loaded');
            testRecipesAPI();
        });
    </script>
</body>
</html>
